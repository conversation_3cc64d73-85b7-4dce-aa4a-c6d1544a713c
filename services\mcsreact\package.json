{"name": "@cktmcs/cktmcs-frontend", "version": "1.0.0", "description": "Frontend for interacting with the mission system", "main": "src/index.tsx", "scripts": {"start": "webpack-dev-server --open", "build": "webpack"}, "dependencies": {"@cktmcs/errorhandler": "file:../../errorhandler", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^5.13.7", "@mui/material": "^5.13.7", "@mui/system": "^5.13.7", "ajv": "^8.17.1", "axios": "^1.12.0", "buffer": "^6.0.3", "cross-spawn": "^7.0.6", "framer-motion": "^11.0.20", "notistack": "^3.0.1", "process": "^0.11.10", "react": "^19.1.1", "react-dom": "^19.1.1", "react-markdown": "^8.0.0", "react-router-dom": "^6.8.2", "stream-browserify": "^3.0.0", "typescript": "^5.6.3", "util": "^0.12.5", "uuid": "^10.0.0", "vis-data": "^7.1.9", "vis-network": "^9.1.9"}, "devDependencies": {"@babel/core": "^7.28.3", "@babel/preset-env": "^7.28.3", "@babel/preset-react": "^7.27.1", "@babel/preset-typescript": "^7.27.1", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@types/jest": "^29.5.14", "@types/react": "^18.3.12", "@types/react-dom": "^18.2.15", "@types/react-router-dom": "^5.3.3", "@types/uuid": "^10.0.0", "babel-loader": "^8.4.1", "buffer": "^6.0.3", "css-loader": "^6.11.0", "html-webpack-plugin": "^5.6.4", "jest": "^29.0.0", "process": "^0.11.10", "style-loader": "^3.3.4", "stream-browserify": "^3.0.0", "ts-jest": "^29.2.5", "ts-node": "^10.9.1", "util": "^0.12.5", "webpack": "^5.101.3", "webpack-cli": "^6.0.1", "webpack-dev-server": "^4.15.2"}, "overrides": {"typescript": "^5.6.3", "glob": "^11.0.1", "inflight": "^2.0.0", "uuid": "^10.0.0", "@svgr/webpack": "^8.1.0", "svgo": "^4.0.0"}, "resolutions": {"typescript": "^5.6.3", "@types/react": "^18.3.12"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}