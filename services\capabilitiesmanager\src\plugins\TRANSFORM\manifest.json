{"id": "plugin-TRANSFORM", "verb": "TRANSFORM", "description": "A plugin for performing general data transformations using code snippets.", "explanation": "This plugin executes a provided Python code snippet to transform input data.", "repository": {"type": "local"}, "inputDefinitions": [{"name": "script", "required": true, "type": "string", "description": "The Python code snippet to execute for transformation. The code has access to a 'params' variable containing the script_parameters. The script should print its result to stdout rather than returning it."}, {"name": "script_parameters", "required": false, "type": "json", "description": "Parameters to be passed to the script. Available as a 'params' variable in the script context. Can be a JSON object or a JSON string."}], "outputDefinitions": [{"name": "transform_result", "required": true, "type": "string", "description": "The output captured from stdout after executing the code snippet. Can be any string data printed by the script."}], "language": "python", "entryPoint": {"main": "main.py", "packageSource": {"type": "local", "path": "./", "requirements": "requirements.txt"}}, "security": {"permissions": [], "sandboxOptions": {}, "trust": {"signature": ""}}, "distribution": {"downloads": 0, "rating": 0}, "version": "1.0.0", "metadata": {"author": "Stage7 Development Team", "tags": ["data", "transform", "code", "utility"], "category": "data-processing", "license": "MIT"}}