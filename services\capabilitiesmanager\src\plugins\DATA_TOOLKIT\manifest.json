{"id": "plugin-DATA_TOOLKIT", "verb": "DATA_TOOLKIT", "description": "A set of tools for processing and manipulating structured data formats like JSON and CSV.", "explanation": "This plugin provides commands to parse, query, and transform data from various sources, which is a critical capability for data analysis and reporting.", "inputGuidance": "Inputs for this plugin must include 'operation'.", "inputDefinitions": [{"name": "operation", "type": "string", "required": true, "description": "The data operation to perform.", "enum": ["query_json", "validate_json", "csv_to_json", "json_to_csv"]}, {"name": "json_object", "type": "object", "required": false, "description": "The JSON object to query. Used with 'query_json'."}, {"name": "query", "type": "object", "required": false, "description": "A dictionary of key-value pairs to filter a list of JSON objects. Used with 'query_json'."}, {"name": "json_string", "type": "string", "required": false, "description": "The JSON string to validate. Used with 'validate_json'."}, {"name": "csv_data", "type": "string", "required": false, "description": "The CSV data as a string. Used with 'csv_to_json'."}, {"name": "json_data", "type": "array", "required": false, "description": "A JSON array of objects. Used with 'json_to_csv'."}], "outputDefinitions": [{"name": "result", "type": "any", "required": true, "description": "The result of the data operation. The structure depends on the operation performed."}], "language": "python", "entryPoint": {"main": "main.py", "function": "execute_plugin"}, "repository": {"type": "local"}, "security": {"permissions": []}, "version": "1.1.0", "metadata": {"author": "Stage7 Development Team", "tags": ["data", "json", "csv", "toolkit"], "category": "utility", "license": "MIT"}}