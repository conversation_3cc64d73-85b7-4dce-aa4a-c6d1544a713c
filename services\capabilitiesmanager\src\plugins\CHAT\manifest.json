{"id": "plugin-CHAT", "verb": "CHAT", "description": "Sends a message to the user and waits for their response. Use ASK_USER_QUESTION instead for questions requiring user input. CHAT is primarily for notifications, status updates, or conversational interactions where you need to inform the user of something.", "explanation": "This plugin sends a message to the user and returns their response. However, for structured questions or when you need specific user input, use ASK_USER_QUESTION instead. CHAT is better suited for conversational interactions, notifications, or status updates.", "language": "python", "inputDefinitions": [{"name": "message", "type": "string", "description": "The message to send to the user", "required": true}], "outputDefinitions": [{"name": "response", "type": "string", "description": "The user's response to the message"}], "entryPoint": {"main": "main.py", "packageSource": {"type": "local", "path": "./"}}, "inputGuidance": "You must include an input named 'message' in your inputs.", "api": {"type": "openapi", "url": "file://openapi.json"}, "repository": {"type": "local"}, "security": {"permissions": [], "sandboxOptions": {}}, "distribution": {"downloads": 0, "rating": 0}, "version": "1.0.0"}