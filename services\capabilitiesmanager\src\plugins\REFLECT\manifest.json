{"id": "plugin-REFLECT", "name": "REFLECT", "verb": "REFLECT", "actionVerb": "REFLECT", "description": "Reflects on the current state of a mission to evaluate progress and determine next steps.", "inputDefinitions": [{"name": "missionId", "description": "The ID of the mission to reflect on. The plugin will use this to fetch the mission goal and other context from the librarian.", "type": "string", "required": false}, {"name": "plan_history", "description": "A JSON string representing the list of executed steps, their status, and parameters.", "type": "string", "required": false}, {"name": "work_products", "description": "A JSON string representing a manifest of data artifacts created by the plan.", "type": "string", "required": false}, {"name": "question", "description": "The specific question to reflect on.", "type": "string", "required": true}], "outputDefinitions": [{"name": "plan", "required": false, "type": "plan", "description": "A detailed, step-by-step plan to achieve the goal. Each step in the plan should be a concrete action that can be executed by another plugin. The plan should be comprehensive and sufficient to fully accomplish the goal."}, {"name": "answer", "required": false, "type": "string", "description": "A direct answer or result, to be used only if the goal can be fully accomplished in a single step without requiring a plan."}], "language": "python", "plugin_type": "python", "security": {"permissions": ["net.fetch"]}, "entryPoint": {"main": "main.py"}, "repository": {"type": "local"}, "version": "1.0.0"}