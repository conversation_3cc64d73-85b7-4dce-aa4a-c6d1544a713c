/* CSS Variables for Light Mode */
:root {
  --modal-bg: #fff;
  --modal-backdrop: rgba(0, 0, 0, 0.4);
  --text-color: #333;
  --modal-shadow: rgba(0, 0, 0, 0.18);
  --button-submit-bg: #1976d2;
  --button-submit-color: #fff;
  --button-submit-hover: #115293;
  --button-cancel-bg: #eee;
  --button-cancel-color: #333;
  --button-cancel-hover: #ccc;
  --input-border: #ccc;
  --file-drop-border: #ccc;
  --file-drop-bg: #fafafa;
  --file-drop-hover-border: #1976d2;
  --file-drop-hover-bg: #f5f5f5;
  --file-drop-drag-border: #1976d2;
  --file-drop-drag-bg: rgba(25, 118, 210, 0.1);
  --file-drop-has-file-border: #4caf50;
  --file-drop-has-file-bg: rgba(76, 175, 80, 0.1);
  --upload-icon-opacity: 0.6;
  --upload-title-color: #333;
  --upload-subtitle-color: #666;
  --file-info-bg: #f0f0f0;
  --file-name-color: #333;
  --file-size-color: #666;
  --remove-file-bg: #ff4444;
  --remove-file-color: white;
  --remove-file-hover: #cc0000;
}

/* Dark Mode Variables */
.user-input-modal.dark {
  --modal-bg: #333;
  --modal-backdrop: rgba(0, 0, 0, 0.6);
  --text-color: #fff;
  --modal-shadow: rgba(0, 0, 0, 0.5);
  --button-submit-bg: #555;
  --button-submit-color: #fff;
  --button-submit-hover: #777;
  --button-cancel-bg: #555;
  --button-cancel-color: #fff;
  --button-cancel-hover: #777;
  --input-border: #666;
  --file-drop-border: #666;
  --file-drop-bg: #444;
  --file-drop-hover-border: #1976d2;
  --file-drop-hover-bg: #555;
  --file-drop-drag-border: #1976d2;
  --file-drop-drag-bg: rgba(25, 118, 210, 0.2);
  --file-drop-has-file-border: #4caf50;
  --file-drop-has-file-bg: rgba(76, 175, 80, 0.2);
  --upload-icon-opacity: 0.8;
  --upload-title-color: #fff;
  --upload-subtitle-color: #ccc;
  --file-info-bg: #555;
  --file-name-color: #fff;
  --file-size-color: #ccc;
  --remove-file-bg: #ff4444;
  --remove-file-color: white;
  --remove-file-hover: #cc0000;
}

.user-input-modal {
  position: fixed;
  top: 0; left: 0; right: 0; bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}
.user-input-modal .modal-backdrop {
  position: fixed;
  top: 0; left: 0; right: 0; bottom: 0;
  background: var(--modal-backdrop);
  z-index: 1001;
}
.user-input-modal .modal-content {
  background: var(--modal-bg);
  border-radius: 8px;
  padding: 2rem 2.5rem;
  box-shadow: 0 4px 32px var(--modal-shadow);
  z-index: 1002;
  min-width: 320px;
  max-width: 90vw;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: stretch;
  color: var(--text-color);
}
.user-input-modal .modal-question {
  font-size: 1.1rem;
  margin-bottom: 1rem;
  color: var(--text-color);
}
.user-input-modal .modal-input {
  margin-bottom: 1.5rem;
}
.user-input-modal .modal-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
}
.user-input-modal .modal-submit {
  background: var(--button-submit-bg);
  color: var(--button-submit-color);
  border: none;
  border-radius: 4px;
  padding: 0.5rem 1.2rem;
  font-size: 1rem;
  cursor: pointer;
}
.user-input-modal .modal-cancel {
  background: var(--button-cancel-bg);
  color: var(--button-cancel-color);
  border: none;
  border-radius: 4px;
  padding: 0.5rem 1.2rem;
  font-size: 1rem;
  cursor: pointer;
}
.user-input-modal input[type="text"],
.user-input-modal input[type="number"] {
  width: 100%;
  padding: 0.5rem;
  font-size: 1rem;
  border: 1px solid var(--input-border);
  border-radius: 4px;
  background: var(--modal-bg);
  color: var(--text-color);
}
.user-input-modal button {
  transition: background 0.2s;
}
.user-input-modal .modal-submit:hover {
  background: var(--button-submit-hover);
}
.user-input-modal .modal-cancel:hover {
  background: var(--button-cancel-hover);
}

/* File Upload Styles */
.file-upload-container {
  width: 100%;
}

.file-drop-zone {
  border: 2px dashed var(--file-drop-border);
  border-radius: 8px;
  padding: 2rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background-color: var(--file-drop-bg);
  min-height: 120px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 1rem;
}

.file-drop-zone:hover {
  border-color: var(--file-drop-hover-border);
  background-color: var(--file-drop-hover-bg);
}

.file-drop-zone.drag-over {
  border-color: var(--file-drop-drag-border);
  background-color: var(--file-drop-drag-bg);
  border-style: solid;
}

.file-drop-zone.has-file {
  border-color: var(--file-drop-has-file-border);
  background-color: var(--file-drop-has-file-bg);
}

.upload-icon {
  font-size: 3rem;
  opacity: var(--upload-icon-opacity);
}

.upload-text {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.upload-title {
  font-size: 1.1rem;
  font-weight: 500;
  color: var(--upload-title-color);
}

.upload-subtitle {
  font-size: 0.9rem;
  color: var(--upload-subtitle-color);
}

.file-info {
  margin-top: 1rem;
  padding: 0.75rem;
  background-color: var(--file-info-bg);
  border-radius: 4px;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.file-name {
  font-weight: 500;
  color: var(--file-name-color);
  flex: 1;
}

.file-size {
  font-size: 0.9rem;
  color: var(--file-size-color);
}

.remove-file {
  background: var(--remove-file-bg);
  color: var(--remove-file-color);
  border: none;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.8rem;
  line-height: 1;
}

.remove-file:hover {
  background: var(--remove-file-hover);
}
