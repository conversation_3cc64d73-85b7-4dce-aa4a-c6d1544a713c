# Use an official Node.js runtime as the base image
FROM node:20-alpine

# Set the working directory in the container
WORKDIR /usr/src/app

# Copy package.json and package-lock.json to the container
COPY package*.json ./
COPY services/librarian/package*.json ./services/librarian/
COPY shared/package*.json ./shared/

# Install dependencies
RUN npm install --workspace=shared
RUN npm install --workspace=services/librarian

# Copy the shared module
COPY shared/ ./shared/
COPY errorhandler ./errorhandler
COPY services/librarian ./services/librarian

# Build the TypeScript code - ensure shared is built first
RUN cd shared && npm run build
RUN cd errorhandler && npm run build
# Make sure the shared package is properly built before building the postoffice service
RUN npm run build --workspace=services/librarian

# Set the working directory to the librarian service

WORKDIR /usr/src/app/services/librarian

# Expose the port the app runs on
EXPOSE 5040

# Install MongoDB client for initialization script
RUN npm install mongodb

# Define the command to run the initialization script and then the application
CMD ["sh", "-c", "node src/init-mongo-data.js && node dist/Librarian.js"]
