import { PluginDefinition, PluginParameterType, PluginManifest, PluginRepositoryType, PluginLocator, compareVersions } from '@cktmcs/shared'; // Added compareVersions
import path from 'path';
import os from 'os';
import { promisify } from 'util';
import { exec } from 'child_process';
import fs from 'fs/promises';
import * as fsSync from 'fs';
import { PluginMarketplace } from '@cktmcs/marketplace';

const execAsync = promisify(exec);

// Define internal verbs as PluginManifest objects
const INTERNAL_VERBS: PluginManifest[] = [
    {
        id: 'internal-THINK',
        verb: 'THINK',
        version: '1.0.0',
        description: 'Allows the agent to perform internal reasoning and analysis based on a given prompt.',
        language: 'internal',
        inputDefinitions: [
            {
                name: 'prompt',
                description: 'The prompt or question for the agent to think about.',
                type: PluginParameterType.STRING,
                required: true,
            },
        ],
        outputDefinitions: [
            {
                name: 'thought',
                description: "The result of the agent's thinking process.",
                type: PluginParameterType.STRING,
                required: true,
            },
        ],
        repository: {
            type: 'internal',
            url: 'internal',
        },
        security: {
            permissions: [],
            sandboxOptions: {allowEval: true, timeout: 30000, memory: 256, allowedAPIs: [], allowedModules: []},
            trust: {
                signature: 'internal',
            },
        },
    },
    {
        id: 'internal-REFLECT',
        verb: 'REFLECT',
        version: '1.0.0',
        description: 'Allows the agent to reflect on its mission, plan, and work products.',
        language: 'internal',
        inputDefinitions: [
            {
                name: 'missionId',
                description: 'The ID of the current mission.',
                type: PluginParameterType.STRING,
                required: true,
            },
            {
                name: 'plan_history',
                description: 'The history of executed steps in the plan.',
                type: PluginParameterType.STRING,
                required: true,
            },
            {
                name: 'work_products',
                description: 'A manifest of data artifacts created during the mission.',
                type: PluginParameterType.STRING,
                required: true,
            },
            {
                name: 'question',
                description: 'The question or prompt for the agent to reflect upon.',
                type: PluginParameterType.STRING,
                required: true,
            },
        ],
        outputDefinitions: [
            {
                name: 'reflection_results',
                description: 'The results of the reflection process.',
                type: PluginParameterType.STRING,
                required: true,
            },
        ],
        repository: {
            type: 'internal',
            url: 'internal',
        },
        security: {
            permissions: [],
            sandboxOptions: {allowEval: true, timeout: 30000, memory: 256, allowedAPIs: [], allowedModules: []},
            trust: {
                signature: 'internal',
            },
        },
    },
    {
        id: 'internal-GENERATE',
        verb: 'GENERATE',
        version: '1.0.0',
        description: 'Uses LLM services to generate content from a prompt or other content. Services include image creation, audio transcription, image editing, etc.',
        language: 'internal',
        inputDefinitions: [
            { name: 'conversationType', type: PluginParameterType.STRING, required: true, description: 'Type of content to generate.' },
            { name: 'modelName', type: PluginParameterType.STRING, required: false, description: 'The specific model to use.' },
            { name: 'optimization', type: PluginParameterType.STRING, required: false, description: 'Optimization strategy.' },
            { name: 'prompt', type: PluginParameterType.STRING, required: false, description: 'Text prompt for generation.' },
            { name: 'file', type: PluginParameterType.OBJECT, required: false, description: 'File for generation context.' },
            { name: 'audio', type: PluginParameterType.OBJECT, required: false, description: 'Audio for generation context.' },
            { name: 'video', type: PluginParameterType.OBJECT, required: false, description: 'Video for generation context.' },
            { name: 'image', type: PluginParameterType.OBJECT, required: false, description: 'Image for generation context.' }
        ],
        outputDefinitions: [
            {
                name: 'generated_content',
                description: 'Content generated by LLM service.',
                type: PluginParameterType.ANY,
                required: true,
            },
        ],
        repository: {
            type: 'internal',
            url: 'internal',
        },
        security: {
            permissions: [],
            sandboxOptions: {allowEval: true, timeout: 30000, memory: 256, allowedAPIs: [], allowedModules: []},
            trust: {
                signature: 'internal',
            },
        },
    },
    {
        id: 'internal-IF_THEN',
        verb: 'IF_THEN',
        version: '1.0.0',
        description: 'Conditional branching based on a condition.',
        language: 'internal',
        inputDefinitions: [
            { name: 'condition', type: PluginParameterType.OBJECT, required: true, description: 'Condition to evaluate. e.g. {"inputName": "value"}' },
            { name: 'trueSteps', type: PluginParameterType.ARRAY, required: true, description: 'Steps to execute if condition is true.' },
            { name: 'falseSteps', type: PluginParameterType.ARRAY, required: false, description: 'Steps to execute if condition is false.' }
        ],
        outputDefinitions: [
            {
                name: 'steps',
                description: 'The steps to be executed based on the condition.',
                type: PluginParameterType.PLAN,
                required: false,
            },
        ],
        repository: {
            type: 'internal',
            url: 'internal',
        },
        security: {
            permissions: [],
            sandboxOptions: {allowEval: true, timeout: 30000, memory: 256, allowedAPIs: [], allowedModules: []},
            trust: {
                signature: 'internal',
            },
        },
    },
    {
        id: 'internal-WHILE',
        verb: 'WHILE',
        version: '1.0.0',
        description: 'Repeat steps while a condition is true.',
        language: 'internal',
        inputDefinitions: [
            { name: 'condition', type: PluginParameterType.OBJECT, required: true, description: 'Condition to evaluate for each loop. e.g. {"inputName": "value"}' },
            { name: 'steps', type: PluginParameterType.ARRAY, required: true, description: 'Steps to execute in each iteration.' }
        ],
        outputDefinitions: [
            {
                name: 'steps',
                description: 'The steps to be executed in the loop.',
                type: PluginParameterType.PLAN,
                required: false,
            },
        ],
        repository: {
            type: 'internal',
            url: 'internal',
        },
        security: {
            permissions: [],
            sandboxOptions: {allowEval: true, timeout: 30000, memory: 256, allowedAPIs: [], allowedModules: []},
            trust: {
                signature: 'internal',
            },
        },
    },
    {
        id: 'internal-UNTIL',
        verb: 'UNTIL',
        version: '1.0.0',
        description: 'Repeat steps until a condition becomes true.',
        language: 'internal',
        inputDefinitions: [
            { name: 'condition', type: PluginParameterType.OBJECT, required: true, description: 'Condition to evaluate after each loop. e.g. {"inputName": "value"}' },
            { name: 'steps', type: PluginParameterType.ARRAY, required: true, description: 'Steps to execute in each iteration.' }
        ],
        outputDefinitions: [
            {
                name: 'steps',
                description: 'The steps to be executed in the loop.',
                type: PluginParameterType.PLAN,
                required: false,
            },
        ],
        repository: {
            type: 'internal',
            url: 'internal',
        },
        security: {
            permissions: [],
            sandboxOptions: {allowEval: true, timeout: 30000, memory: 256, allowedAPIs: [], allowedModules: []},
            trust: {
                signature: 'internal',
            },
        },
    },
    {
        id: 'internal-SEQUENCE',
        verb: 'SEQUENCE',
        version: '1.0.0',
        description: 'Execute steps in strict sequential order / no concurrency.',
        language: 'internal',
        inputDefinitions: [
            { name: 'steps', type: PluginParameterType.ARRAY, required: true, description: 'Steps to execute sequentially.' }
        ],
        outputDefinitions: [
            {
                name: 'steps',
                description: 'The steps to be executed sequentially.',
                type: PluginParameterType.PLAN,
                required: false,
            },
        ],
        repository: {
            type: 'internal',
            url: 'internal',
        },
        security: {
            permissions: [],
            sandboxOptions: {allowEval: true, timeout: 30000, memory: 256, allowedAPIs: [], allowedModules: []},
            trust: {
                signature: 'internal',
            },
        },
    },
    {
        id: 'internal-TIMEOUT',
        verb: 'TIMEOUT',
        version: '1.0.0',
        description: 'Set a timeout for a group of steps.',
        language: 'internal',
        inputDefinitions: [
            { name: 'timeout', type: PluginParameterType.NUMBER, required: true, description: 'Timeout in milliseconds.' },
            { name: 'steps', type: PluginParameterType.ARRAY, required: true, description: 'Steps to execute with a timeout.' }
        ],
        outputDefinitions: [
            {
                name: 'steps',
                description: 'The steps to be executed with a timeout.',
                type: PluginParameterType.PLAN,
                required: false,
            },
        ],
        repository: {
            type: 'internal',
            url: 'internal',
        },
        security: {
            permissions: [],
            sandboxOptions: {allowEval: true, timeout: 30000, memory: 256, allowedAPIs: [], allowedModules: []},
            trust: {
                signature: 'internal',
            },
        },
    },
    {
        id: 'internal-REPEAT',
        verb: 'REPEAT',
        version: '1.0.0',
        description: 'Repeat steps a specific number of times.',
        language: 'internal',
        inputDefinitions: [
            { name: 'count', type: PluginParameterType.NUMBER, required: true, description: 'Number of times to repeat.' },
            { name: 'steps', type: PluginParameterType.ARRAY, required: true, description: 'Steps to repeat.' }
        ],
        outputDefinitions: [
            {
                name: 'steps',
                description: 'The steps to be repeated.',
                type: PluginParameterType.PLAN,
                required: false,
            },
        ],
        repository: {
            type: 'internal',
            url: 'internal',
        },
        security: {
            permissions: [],
            sandboxOptions: {allowEval: true, timeout: 30000, memory: 256, allowedAPIs: [], allowedModules: []},
            trust: {
                signature: 'internal',
            },
        },
    },
    {
        id: 'internal-FOREACH',
        verb: 'FOREACH',
        version: '1.0.0',
        description: 'Iterate over an array and execute steps for each item.',
        language: 'internal',
        inputDefinitions: [
            { name: 'array', type: PluginParameterType.ARRAY, required: true, description: 'Array to iterate over.' },
            { name: 'steps', type: PluginParameterType.ARRAY, required: true, description: 'A plan of steps to execute for each item.' }
        ],
        outputDefinitions: [
            {
                name: 'steps',
                description: 'The steps to be executed for each item in the array.',
                type: PluginParameterType.PLAN,
                required: false,
            },
            {
                name: 'loop_skipped',
                description: 'Indicates if the loop was skipped due to an empty input array.',
                type: PluginParameterType.STRING,
                required: false,
            },
        ],
        repository: {
            type: 'internal',
            url: 'internal',
        },
        security: {
            permissions: [],
            sandboxOptions: {allowEval: true, timeout: 30000, memory: 256, allowedAPIs: [], allowedModules: []},
            trust: {
                signature: 'internal',
            },
        },
    },
];


export class PluginRegistry {
    private cache: Map<string, PluginRepositoryType>;
    private verbIndex: Map<string, string>;  // verb -> id mapping
    private pluginMarketplace: PluginMarketplace;
    public currentDir: string; // Base directory for inline plugins, e.g., services/capabilitiesmanager/src

    /**
     * Get the plugin marketplace instance
     * @returns PluginMarketplace instance
     */
    public getPluginMarketplace(): PluginMarketplace {
        return this.pluginMarketplace;
    }

    /**
     * Update the plugin marketplace instance
     * This is used when configuration changes and we need to reinitialize repositories
     * @param marketplace New PluginMarketplace instance
     */
    public updatePluginMarketplace(marketplace: PluginMarketplace): void {
        this.pluginMarketplace = marketplace;
        console.log('Plugin marketplace updated with new configuration');
        
        // Refresh the cache with the new marketplace
        this.refreshCache().catch(error => {
            console.error('Failed to refresh plugin cache after marketplace update:', error);
        });
    }

    constructor() {
        this.cache = new Map();
        this.verbIndex = new Map();
        this.pluginMarketplace = new PluginMarketplace();
        // Set currentDir to be 'services/capabilitiesmanager/src'
        // so inline plugins are resolved from 'services/capabilitiesmanager/src/plugins/{verb}'
        this.currentDir = path.resolve(__dirname, '..');
        // Defensive: ensure pluginMarketplace is initialized before use
        if (!this.pluginMarketplace || typeof this.pluginMarketplace.getRepositories !== 'function') {
            throw new Error('PluginRegistry: pluginMarketplace is not initialized or invalid.');
        }
        this.initialize();
    }

    private async _prepareGitPlugin(manifest: PluginManifest, targetDir: string): Promise<void> {
        if (!manifest.packageSource || manifest.packageSource.type !== 'git' || !manifest.packageSource.url) {
            throw new Error('Invalid manifest: packageSource must be of type git and include a URL.');
        }
        const { url, branch, commitHash } = manifest.packageSource;
        const branchToClone = branch || 'main'; // Default to 'main' if no branch specified

        try {
            console.log(`Cloning plugin ${manifest.id} from ${url} (branch: ${branchToClone}) into ${targetDir}`);
            await execAsync(`git clone --depth 1 --branch ${branchToClone} ${url} ${targetDir}`);

            if (commitHash) {
                console.log(`Checking out commit ${commitHash} for plugin ${manifest.id} in ${targetDir}`);
                await execAsync(`git -C ${targetDir} checkout ${commitHash}`);
            }
            console.log(`Plugin ${manifest.id} prepared successfully in ${targetDir}`);
        } catch (error) {
            console.error(`Error preparing git plugin ${manifest.id} from ${url}:`, error);
            // Attempt to clean up partially cloned directory
            try {
                await fs.rm(targetDir, { recursive: true, force: true });
            } catch (cleanupError) {
                console.error(`Error cleaning up failed git clone for ${manifest.id} at ${targetDir}:`, cleanupError);
            }
            throw new Error(`Failed to prepare git plugin: ${error instanceof Error ? error.message : String(error)}`);
        }
    }

    public async preparePluginForExecution(manifest: PluginManifest): Promise<{ pluginRootPath: string; effectiveManifest: PluginManifest }> {
        if (manifest.packageSource && manifest.packageSource.type === 'git') {
            const cacheDirBase = path.join(os.homedir(), '.cktmcs', 'plugin_cache', manifest.id);
            // Use commitHash for definitive versioning, fallback to branch, then 'latest_main'
            const versionSpecificComponent = manifest.packageSource.commitHash || manifest.packageSource.branch || 'latest_main';
            // Sanitize versionSpecificComponent to be directory-friendly (e.g., replace slashes in branch names)
            const sanitizedVersionComponent = versionSpecificComponent.replace(/[/\\]/g, '_');
            const cacheDir = path.join(cacheDirBase, sanitizedVersionComponent);

            console.log(`Preparing git plugin ${manifest.id}. Target cache directory: ${cacheDir}`);

            const dirExists = await fs.stat(cacheDir).then(() => true).catch(() => false);

            if (!dirExists) {
                console.log(`Cache directory ${cacheDir} not found. Creating and cloning...`);
                await fs.mkdir(cacheDir, { recursive: true });
                await this._prepareGitPlugin(manifest, cacheDir);
            } else {
                console.log(`Cache directory ${cacheDir} found. Verifying Git repository...`);
                try {
                    // Check if it's a valid git repository
                    await execAsync(`git -C ${cacheDir} rev-parse --is-inside-work-tree`);
                    console.log(`Git repository in ${cacheDir} is valid. Using existing clone.`);
                    // Optional: pull latest changes if not pinned to a commitHash
                    if (!manifest.packageSource.commitHash && manifest.packageSource.branch) {
                        console.log(`Pulling latest changes for branch ${manifest.packageSource.branch} in ${cacheDir}...`);
                        await execAsync(`git -C ${cacheDir} pull origin ${manifest.packageSource.branch}`);
                    }
                } catch (gitCheckError) {
                    console.warn(`Git repository in ${cacheDir} is invalid or corrupted. Removing and re-cloning...`, gitCheckError);
                    await fs.rm(cacheDir, { recursive: true, force: true });
                    await fs.mkdir(cacheDir, { recursive: true });
                    await this._prepareGitPlugin(manifest, cacheDir);
                }
            }

            const pluginRootPath = path.join(cacheDir, manifest.packageSource.subPath || '');
            console.log(`Plugin root path for ${manifest.id}: ${pluginRootPath}`);
            return { pluginRootPath, effectiveManifest: manifest };
        } else {
            // Handle 'inline' plugins or plugins without packageSource (legacy)
            // First try to find the plugin directory by verb name
            let pluginRootPath = path.join(this.currentDir, 'plugins', manifest.verb);

            // If the verb-named directory doesn't exist, scan for the correct directory
            if (!fsSync.existsSync(pluginRootPath)) {
                const pluginsDir = path.join(this.currentDir, 'plugins');
                try {
                    const dirs = fsSync.readdirSync(pluginsDir);
                    for (const dir of dirs) {
                        const manifestPath = path.join(pluginsDir, dir, 'manifest.json');
                        if (fsSync.existsSync(manifestPath)) {
                            try {
                                const dirManifest = JSON.parse(fsSync.readFileSync(manifestPath, 'utf-8'));
                                if (dirManifest.verb === manifest.verb || dirManifest.id === manifest.id) {
                                    pluginRootPath = path.join(pluginsDir, dir);
                                    break;
                                }
                            } catch (e) {
                                // Skip invalid manifest files
                                continue;
                            }
                        }
                    }
                } catch (e) {
                    // If scanning fails, fall back to the original path
                }
            }

            console.log(`Using inline plugin path for ${manifest.id} (${manifest.verb}): ${pluginRootPath}`);
            return { pluginRootPath, effectiveManifest: manifest };
        }
    }

    private _registerInternalVerbs(): void {
        for (const manifest of INTERNAL_VERBS) {
            this.cache.set(manifest.id, 'internal' as PluginRepositoryType);
            this.verbIndex.set(manifest.verb, manifest.id);
        }
        console.log(`Registered ${INTERNAL_VERBS.length} internal verbs.`);
    }

    public async initialize(): Promise<void> {
        // Initialize existing plugins by refreshing the cache
        try {
            await this.refreshCache();
            console.log("PluginRegistry initialized and cache populated.");
            // Log all registered verbs and plugin ids for diagnosis
            console.log('PluginRegistry: Registered verbs after cache refresh:', Array.from(this.verbIndex.keys()));
            console.log('PluginRegistry: Registered plugin ids after cache refresh:', Array.from(this.cache.keys()));
        } catch (error) {
            console.error("PluginRegistry.initialize: Failed to refresh plugin cache during initialization", error);
            // Decide if we should throw or continue with an empty/partially initialized registry
            // For now, log the error and continue; the registry might be partially functional or recover.
        }
    }

    async fetchOne(id: string, version?: string, repository?: PluginRepositoryType): Promise<PluginManifest | undefined> { // Added version parameter
        try {
            // Check internal verbs first
            if (id.startsWith('internal-')) {
                const internalPlugin = INTERNAL_VERBS.find(
                    (manifest) => manifest.id === id && (!version || manifest.version === version)
                );
                if (internalPlugin) {
                    return internalPlugin;
                }
            }

            const plugin = await this.pluginMarketplace.fetchOne(id, version, repository);
            if (plugin && !this.cache.has(plugin.id)) {
                // Removed all updateCache calls
            }
            return plugin;
        } catch (err) {
            console.warn(`pluginRegistry: fetchOne failed for id=${id}, repository=${repository}:`, err);
            return undefined;
        }
    }

    async fetchOneByVerb(verb: string, version?: string): Promise<PluginManifest | undefined> {
        if (!this.pluginMarketplace || typeof this.pluginMarketplace.fetchOneByVerb !== 'function') {
            console.error('PluginRegistry: pluginMarketplace is not initialized or fetchOneByVerb is not available.');
            return undefined;
        }
        console.log('PluginRegistry.fetchOneByVerb called for verb:', verb);
        try {
            // Check internal verbs first
            const internalPlugin = INTERNAL_VERBS.find(
                (manifest) => manifest.verb === verb && (!version || manifest.version === version)
            );
            if (internalPlugin) {
                return internalPlugin;
            }

            if (this.verbIndex.has(verb)) {
                const id = this.verbIndex.get(verb);
                if (!id) {
                    return undefined;
                }
                const repository = this.cache.get(id);
                return this.pluginMarketplace.fetchOne(id, version, repository as PluginRepositoryType);
            }
            const plugin = await this.pluginMarketplace.fetchOneByVerb(verb);
            if (plugin && !this.cache.has(plugin.id)) {
                // Removed all updateCache calls
            }
            return plugin;
        } catch (err) {
            console.warn(`pluginRegistry: fetchOneByVerb failed for verb=${verb}:`, err);
            return undefined;
        }
    }

    /**
     * Fetches all available versions of a plugin by its ID.
     * Assumes the PluginMarketplace will have a method like `fetchAllVersionsOfPlugin`.
     */
    async fetchAllVersionsOfPlugin(pluginId: string, repositoryType?: PluginRepositoryType): Promise<PluginManifest[] | undefined> {
        console.log(`PluginRegistry: Fetching all versions for plugin ID ${pluginId} from repository ${repositoryType || 'default'}`);
        try {
            const versions = await this.pluginMarketplace.fetchAllVersionsOfPlugin(pluginId, repositoryType as PluginRepositoryType);
            if (versions && versions.length > 0) {
                versions.sort((a: PluginManifest, b: PluginManifest) => compareVersions(b.version, a.version));
                return versions;
            }
            return undefined;
        } catch (error) {
            console.warn(`PluginRegistry: Error fetching all versions for plugin ID ${pluginId}:`, error);
            return undefined;
        }
    }

    /**
     * Fetches all available versions of a plugin by its verb.
     * This first resolves the verb to a plugin ID.
     */
    async fetchAllVersionsByVerb(verb: string, repositoryType?: PluginRepositoryType): Promise<PluginManifest[] | undefined> {
        console.log(`PluginRegistry: Fetching all versions for verb ${verb} from repository ${repositoryType || 'default'}`);
        try {
            const anyVersionPlugin = await this.fetchOneByVerb(verb);
            if (!anyVersionPlugin) {
                console.warn(`PluginRegistry: No plugin found for verb ${verb} to determine plugin ID.`);
                return undefined;
            }
            const pluginId = anyVersionPlugin.id;
            return this.fetchAllVersionsOfPlugin(pluginId, repositoryType);
        } catch (err) {
            console.warn(`PluginRegistry: fetchAllVersionsByVerb failed for verb=${verb}:`, err);
            return undefined;
        }
    }

    async findOne(id: string, version?: string): Promise<PluginManifest | undefined> { // Added version
        try {
            if (this.cache.has(id)) {
                const repository = this.cache.get(id);
                return this.pluginMarketplace.fetchOne(id, version, repository as PluginRepositoryType);
            }
            const plugin = await this.pluginMarketplace.fetchOne(id, version);
            if (plugin && !this.cache.has(plugin.id)) {
                // Removed all updateCache calls
            }
            return plugin;
        } catch (err) {
            console.warn(`pluginRegistry: findOne failed for id=${id}:`, err);
            return undefined;
        }
    }

    public async store(plugin: PluginManifest): Promise<void> {
        try {
            await this.pluginMarketplace.store(plugin);
        } catch (err) {
            console.warn(`pluginRegistry: store failed for plugin id=${plugin.id}:`, err);
        }
    }

    /**
     * Delete a plugin by ID (and optionally version) from a specific repository
     */
    public async delete(pluginId: string, version?: string, repository?: PluginRepositoryType): Promise<void> {
        try {
            if (typeof (this.pluginMarketplace as any).delete === 'function') {
                await (this.pluginMarketplace as any).delete(pluginId, version, repository);
            }
        } catch (err) {
            console.warn(`pluginRegistry: delete failed for plugin id=${pluginId}:`, err);
        }
    }

    private getLocatorFromManifest(manifest: PluginManifest): PluginLocator {
        return {
            id: manifest.id,
            verb: manifest.verb,
            repository: {
                type: manifest.repository.type,
                url: manifest.repository.url,
                dependencies: manifest.repository.dependencies
            }
        };
    }

    /**
     * Refresh the plugin cache from all repositories
     */
    private async refreshCache(): Promise<void> {
        if (!this.pluginMarketplace || typeof this.pluginMarketplace.getRepositories !== 'function') {
            throw new Error('PluginRegistry: pluginMarketplace is not initialized or invalid in refreshCache.');
        }
        try {
            console.log('Refreshing plugin cache...');
            this.cache.clear();
            this.verbIndex.clear();
            const repositories = this.pluginMarketplace.getRepositories();
            for (const [repoType, repository] of repositories.entries()) {
                try {
                    console.log(`Loading plugins from ${repoType} repository...`);
                    const plugins = await repository.list(); // Lists PluginLocators
                    if (!Array.isArray(plugins)) {
                        console.error(`Failed to list plugins from ${repoType} repository: plugins is not iterable`);
                        continue; // Skip this repository
                    }
                    for (const locator of plugins) {
                        try {
                            const manifest = await repository.fetch(locator.id);
                            if (manifest) {
                                this.cache.set(manifest.id, repoType as PluginRepositoryType);
                                this.verbIndex.set(manifest.verb, manifest.id);
                            }
                        } catch (pluginError) {
                            console.error(`Failed to fetch manifest for plugin ${locator.id} from ${repoType} repository during cache refresh:`, pluginError);
                        }
                    }
                    console.log(`Loaded ${plugins.length} plugins from ${repoType} repository`);
                } catch (repoError) {
                    console.error(`Failed to list plugins from ${repoType} repository:`, repoError);
                }
            }
            console.log(`Plugin cache refreshed. Total plugins: ${this.cache.size}`);
            this._registerInternalVerbs();
        } catch (error) {
            console.error('Failed to refresh plugin cache:', error);
            throw error;
        }
    }

    /**
     * Returns a list of active/configured repository types (for frontend repo picker)
     */
    public getActiveRepositories(): { type: string; label: string }[] {
        // Use the pluginMarketplace instance to get the configured repositories
        const repoMap = this.pluginMarketplace.getRepositories();
        // Map to label for UI
        const labelMap: Record<string, string> = {
            local: 'Local',
            mongo: 'MongoDB',
            github: 'GitHub',
            git: 'Git',
            openapi: 'OpenAPI Tools',
            mcp: 'MCP Tools',
            'librarian-definition': 'Librarian',
        };
        return Array.from(repoMap.keys()).map(type => ({
            type,
            label: labelMap[type] || type
        }));
    }

    public async list(repositoryType?: PluginRepositoryType): Promise<PluginManifest[]> { // Change return type to PluginManifest[]
        try {
            console.log(`Listing plugins from repository type: ${repositoryType || 'all'}`);
            const allPlugins: PluginManifest[] = []; // Collect full manifests

            // Iterate through the cache to get full manifests
            for (const [pluginId, repoTypeInCache] of this.cache.entries()) {
                if (!repositoryType || repoTypeInCache === repositoryType) {
                    // Fetch the full manifest using fetchOne, which retrieves from marketplace
                    const manifest = await this.fetchOne(pluginId, undefined, repoTypeInCache);
                    if (manifest) {
                        allPlugins.push(manifest);
                    }
                }
            }

            console.log(`Found ${allPlugins.length} plugins in total from repository type: ${repositoryType || 'all'}`);
            return allPlugins; // Return full manifests
        } catch (err) {
            console.warn('pluginRegistry: list failed:', err);
            return [];
        }
    }

}