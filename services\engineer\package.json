{"name": "@cktmcs/engineer", "version": "1.0.0", "description": "Central message routing component for the system", "main": "dist/Engineer.ts", "scripts": {"start": "ts-node src/Engineer.ts", "build": "tsc", "test": "jest"}, "dependencies": {"@cktmcs/errorhandler": "file:../../errorhandler", "@cktmcs/marketplace": "file:../../marketplace", "@cktmcs/shared": "file:../../shared", "axios": "^1.12.0", "body-parser": "^1.19.0", "express": "^4.21.1", "simple-git": "^3.27.0", "ws": "^8.18.2"}, "devDependencies": {"@types/express": "^5.0.0", "@types/jest": "^29.5.14", "@types/supertest": "^6.0.2", "jest": "^30.0.5", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "ts-node": "^10.9.1", "typescript": "^5.6.3"}, "jest": {"preset": "ts-jest", "testEnvironment": "node"}}