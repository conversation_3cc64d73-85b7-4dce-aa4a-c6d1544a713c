{"name": "@cktmcs/brain", "version": "1.0.0", "description": "Brain component for handling conversations and selecting the best LLM", "main": "dist/Brain.js", "scripts": {"start": "ts-node src/Brain.ts", "build": "tsc", "test": "jest"}, "dependencies": {"@cktmcs/errorhandler": "file:../../errorhandler", "@cktmcs/shared": "file:../../shared", "@google/generative-ai": "^0.24.0", "@huggingface/inference": "^2.8.1", "axios": "^1.12.0", "body-parser": "^1.19.0", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.21.1", "openai": "^4.68.4", "path": "^0.12.7"}, "devDependencies": {"@types/cors": "^2.8.17", "@types/express": "^5.0.0", "@types/jest": "^29.5.14", "@types/supertest": "^6.0.2", "jest": "^30.0.5", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "ts-node": "^10.9.1", "typescript": "^5.6.3"}, "jest": {"preset": "ts-jest", "testEnvironment": "node"}}