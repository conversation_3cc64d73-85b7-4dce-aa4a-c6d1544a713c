{"id": "plugin-ASK_USER_QUESTION", "verb": "ASK_USER_QUESTION", "description": "Use this to get subjective input, opinions, or approvals from the user. Do not use ask the user to perform tasks or provide information that could be found elsewhere. For example, use it to ask 'Which of these three logos do you prefer?', not to ask 'What is the capital of France?'.", "explanation": "This plugin sends a question to the user and returns their response. It is not intended to be used to offload work to the user.  If should be used for user optinions, decisions or approvals", "inputGuidance": "Inputs for this plugin must include 'question'.", "inputDefinitions": [{"name": "question", "required": true, "type": "string", "description": "The question to ask the user"}, {"name": "choices", "required": false, "type": "array", "description": "Optional array of choices for multiple choice questions"}, {"name": "answerType", "required": false, "type": "string", "description": "Type of answer expected (text, number, boolean, or multipleChoice)"}], "outputDefinitions": [{"name": "answer", "required": false, "type": "string", "description": "The user's response"}], "language": "python", "entryPoint": {"main": "main.py", "packageSource": {"type": "local", "path": "./", "requirements": "requirements.txt"}}, "repository": {"type": "local"}, "security": {"permissions": [], "sandboxOptions": {}, "trust": {"signature": "GEr7jE7Y6GDjfKXD2i1yMrIWPOko7GJxg9jPCNrxCae2pD1pvVXdi0YrTWK4SKGZis1G6GZcoTtrob26xt17Iuu9f8O8gX/Cz433TRKo78Akl5ggnQn8fqj1uQmIco6uGcspMxHF0PuNHTrZF5jKG+jVT2clG7HPkUXEYhRc61kD7Z6MaKAjFmg75JUkyaW5S6hNY1wFnmrTLX37mu017QE+65rZWELHzeGV9nbmataVMzCjPZmcvn583tCZTs+H9sC8iVr8kKwvCJ2Y3grmSnr6/8biKgQLlpIQp9x9vv7TuyMV7obicGkgkfvt6HQquynHZA0ForXKwwYbth3smg=="}}, "distribution": {"downloads": 0, "rating": 0}, "version": "1.0.0"}