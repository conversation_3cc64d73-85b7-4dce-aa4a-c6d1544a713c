import { WorkProduct } from './WorkProduct';
import { MapSerializer, PluginOutput, createAuthenticatedAxios } from '@cktmcs/shared';
import { analyzeError } from '@cktmcs/errorhandler';


export interface AgentState {
    id: string;
    status: string;
    output: any;
    inputs: Map<string, any>;
    missionId: string;
    steps: any[];
    dependencies: string[];
    capabilitiesManagerUrl: string;
    brainUrl: string;
    trafficManagerUrl: string;
    librarianUrl: string;
    conversation: any[];
    missionContext: string;
    role?: string;
    roleCustomizations?: any;
    lastFailedStep?: any;
}

export interface StepEvent {
    eventType: string;
    stepId: string;
    missionId: string;
    timestamp: string;
    error?: {
        message: string;
        stack?: string;
        type?: string;
    };
    context?: any;
    recoveryAttempt?: number;
    strategy?: string;
}

export class AgentPersistenceManager {
    private librarianUrl: string;
    private authenticatedApi: any;
    private securityManagerUrl: string;

    constructor(
        librarianUrl: string = process.env.LIBRARIAN_URL || 'librarian:5040',
        authenticatedApi?: any
    ) {
        this.librarianUrl = librarianUrl;
        this.securityManagerUrl = process.env.SECURITYMANAGER_URL || 'securitymanager:5010';

        // If authenticatedApi is provided, use it, otherwise create a new one
        if (authenticatedApi) {
            this.authenticatedApi = authenticatedApi;
        } else {
            // Create authenticated API client
            this.authenticatedApi = createAuthenticatedAxios(
                'AgentPersistenceManager',
                this.securityManagerUrl,
                process.env.CLIENT_SECRET || 'stage7AuthSecret'
            );
        }
    }

    async saveAgent(agent: AgentState): Promise<void> {
        if (!agent || !agent.id) {
            console.error('Cannot save agent: agent is undefined or has no ID');
            return;
        }

        const stateToSave = MapSerializer.transformForSerialization(agent);

        // Exclude the steps array from the main agent document to prevent it from getting too large
        if (stateToSave.steps) {
            delete stateToSave.steps;
        }

        try {
            await this.authenticatedApi.post(`http://${this.librarianUrl}/storeData`, {
                id: agent.id,
                data: stateToSave,
                storageType: 'mongo',
                collection: 'agents'
            });
        } catch (error) { analyzeError(error as Error);
            console.error(`Error saving agent state for agent ${agent.id}:`, error instanceof Error ? error.message : error);
        }
    }


    async logEvent(event: any): Promise<void> {
        if (!event) {
            console.error('Cannot log event: event is undefined or null');
            return;
        }
        try {
            const eventWithTimestamp = {
                ...event,
                timestamp: new Date().toISOString()
            };
            await this.authenticatedApi.post(`http://${this.librarianUrl}/storeData`, {
                id: event.id || undefined,
                data: eventWithTimestamp,
                storageType: 'mongo',
                collection: 'events'
            });
            console.log(`Event logged successfully: ${JSON.stringify(eventWithTimestamp)}`);
        } catch (error) { analyzeError(error as Error);
            console.error('Error logging event:', error instanceof Error ? error.message : error);
        }
    }

    async loadAgent(agentId: string): Promise<any | null> {
        try {
            const response = await this.authenticatedApi.get(`http://${this.librarianUrl}/loadData/${agentId}`, {
                params: { storageType: 'mongo', collection: 'agents' }
            });
            return MapSerializer.transformFromSerialization(response.data.data);
        } catch (error) { analyzeError(error as Error);
            console.error('Error loading agent state:', error instanceof Error ? error.message : error);
            return null;
        }
    }

    async deleteAgent(agentId: string): Promise<void> {
        // Implement logic to delete agent state from persistent storage
        try {
            await this.authenticatedApi.delete(`http://${this.librarianUrl}/deleteData/${agentId}`, {
                params: { storageType: 'mongo', collection: 'agents' }
            });
            console.log(`Agent ${agentId} deleted successfully`);
        } catch (error) {
            analyzeError(error as Error);
            console.error(`Error deleting agent ${agentId}:`, error instanceof Error ? error.message : error);
        }
    }

    async loadStepsForAgent(agentId: string): Promise<any[]> {
        try {
            const response = await this.authenticatedApi.get(`http://${this.librarianUrl}/queryData`, {
                params: {
                    storageType: 'mongo',
                    collection: 'events',
                    query: {
                        agentId: agentId,
                        eventType: 'step_created'
                    },
                    options: {
                        sort: { timestamp: 1 } // Sort by timestamp to maintain order
                    }
                }
            });
            if (response.data && Array.isArray(response.data.data)) {
                return response.data.data;
            }
            return [];
        } catch (error) {
            analyzeError(error as Error);
            console.error(`Error loading steps for agent ${agentId}:`, error instanceof Error ? error.message : error);
            return [];
        }
    }

    async saveWorkProduct(workProduct: WorkProduct): Promise<void> {
        if (!workProduct || !workProduct.agentId || !workProduct.stepId) {
            console.error('Cannot save work product: missing required fields', workProduct);
            return;
        }

        console.log(`Saving work product for agent ${workProduct.agentId}, step ${workProduct.stepId}`);
        try {
            // Ensure data is properly serialized
            const serializedData = Array.isArray(workProduct.data)
                ? workProduct.data.map(item => {
                    if (item && item.result && typeof item.result === 'object') {
                        return { ...item, result: MapSerializer.transformForSerialization(item.result) };
                    }
                    return item;
                })
                : MapSerializer.transformForSerialization(workProduct.data);

            return await this.authenticatedApi.post(`http://${this.librarianUrl}/storeWorkProduct`, {
                agentId: workProduct.agentId,
                stepId: workProduct.stepId,
                data: serializedData
            });
        } catch (error) { analyzeError(error as Error);
            console.error(`Error saving work product for agent ${workProduct.agentId}, step ${workProduct.stepId}:`, error instanceof Error ? error.message : String(error));
        }
    }

    async loadWorkProduct(agentId: string, stepId: string): Promise<WorkProduct | null> {
        try {
            const response = await this.authenticatedApi.get(
                `http://${this.librarianUrl}/loadData/${agentId}_${stepId}`,
                { params: { storageType: 'mongo', collection: 'work_products' } }
            );
            return response.data.data;
        } catch (error) { analyzeError(error as Error);
            console.error('Error loading work product:', error instanceof Error ? error.message : error);
            return null;
        }
    }

    async getStepEvents(stepId: string): Promise<StepEvent[]> {
        try {
            const response = await this.authenticatedApi.get(`http://${this.librarianUrl}/queryData`, {
                params: {
                    storageType: 'mongo',
                    collection: 'step_events',
                    query: { stepId }
                }
            });
            return response.data.data || [];
        } catch (error) {
            analyzeError(error as Error);
            console.error(`Error getting step events for step ${stepId}:`, error instanceof Error ? error.message : error);
            return [];
        }
    }

    async getStepErrorHistory(stepId: string): Promise<StepEvent[]> {
        try {
            const response = await this.authenticatedApi.get(`http://${this.librarianUrl}/queryData`, {
                params: {
                    storageType: 'mongo',
                    collection: 'step_events',
                    query: { 
                        stepId,
                        eventType: { $in: ['step_error', 'step_failure', 'recovery_attempt'] }
                    }
                }
            });
            return response.data.data || [];
        } catch (error) {
            analyzeError(error as Error);
            console.error(`Error getting error history for step ${stepId}:`, error instanceof Error ? error.message : error);
            return [];
        }
    }

    async getRecoveryAttempts(stepId: string): Promise<number> {
        try {
            const events = await this.getStepEvents(stepId);
            return events.filter(e => 
                e.eventType === 'recovery_attempt' || 
                e.eventType === 'step_retry'
            ).length;
        } catch (error) {
            analyzeError(error as Error);
            console.error(`Error getting recovery attempts for step ${stepId}:`, error instanceof Error ? error.message : error);
            return 0;
        }
    }

    async clearStepHistory(stepId: string): Promise<void> {
        try {
            await this.authenticatedApi.delete(`http://${this.librarianUrl}/deleteData`, {
                params: {
                    storageType: 'mongo',
                    collection: 'step_events',
                    query: { stepId }
                }
            });
        } catch (error) {
            analyzeError(error as Error);
            console.error(`Error clearing history for step ${stepId}:`, error instanceof Error ? error.message : error);
        }
    }

    async loadAllWorkProducts(agentId: string): Promise<WorkProduct[]> {
        if (!agentId) {
            console.error('Cannot load work products: missing agent ID');
            return [];
        }

        try {
            console.log(`Loading all work products for agent ${agentId}`);
            const response = await this.authenticatedApi.get(`http://${this.librarianUrl}/loadAllWorkProducts/${agentId}`);

            if (!response.data || !Array.isArray(response.data)) {
                console.error(`No work products found for agent ${agentId}`);
                return [];
            }

            return response.data.map((wp: any) => {
                if (!wp || !wp.stepId || !wp.data) {
                    console.error(`Invalid work product data for agent ${agentId}:`, wp);
                    return null;
                }
                return new WorkProduct(
                    agentId,
                    wp.stepId,
                    MapSerializer.transformFromSerialization(wp.data) as PluginOutput[]
                );
            }).filter((wp: WorkProduct | null) => wp !== null) as WorkProduct[];
        } catch (error) { analyzeError(error as Error);
            console.error(`Error loading all work products for agent ${agentId}:`, error instanceof Error ? error.message : error);
            return [];
        }
    }

}