{"name": "@cktmcs/shared", "version": "1.0.0", "main": "dist/index.js", "type": "commonjs", "browser": "dist/browser.js", "types": "dist/index.d.ts", "scripts": {"clean": "<PERSON><PERSON><PERSON> dist", "build": "npm run clean && tsc --outDir ./dist && tsc --project tsconfig.browser.json", "build:verbose": "npm run clean && tsc --listEmittedFiles --listFiles && tsc --project tsconfig.browser.json --listEmittedFiles --listFiles", "test": "jest"}, "dependencies": {"amqp-connection-manager": "^4.1.14", "amqplib": "^0.10.7", "axios": "^1.12.0", "isolated-vm": "^5.0.4", "jsonwebtoken": "^9.0.2", "tslib": "^2.6.2", "uuid": "^10.0.0"}, "devDependencies": {"@types/amqplib": "^0.10.7", "@types/jest": "^29.5.14", "@types/jsonwebtoken": "^9.0.7", "@types/node": "^20.11.30", "@types/uuid": "^10.0.0", "jest": "^30.0.5", "rimraf": "^6.0.1", "ts-jest": "^29.2.5", "ts-node": "^10.9.1", "typescript": "5.6.3"}, "jest": {"preset": "ts-jest", "testEnvironment": "node"}, "overrides": {"glob": "^11.0.1", "inflight": "^2.0.0"}}